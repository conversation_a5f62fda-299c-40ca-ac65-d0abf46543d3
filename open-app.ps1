# PowerShell script to open Ikaros Al-Ahliya Construction Management System
Write-Host "Opening Ikaros Al-Ahliya Construction Management System..." -ForegroundColor Green

# Use current directory and wildcard to find the HTML file
$htmlFiles = Get-ChildItem -Path $PSScriptRoot -Filter "*.html" | Where-Object { $_.Name -like "*ايكاروس*" -or $_.Name -like "*اهلية*" }
if ($htmlFiles.Count -eq 0) {
    $htmlFiles = Get-ChildItem -Path $PSScriptRoot -Filter "*.html"
}

if ($htmlFiles.Count -gt 0) {
    $htmlFile = $htmlFiles[0].FullName
}
else {
    $htmlFile = Join-Path $PSScriptRoot "index.html"
}

if (Test-Path $htmlFile) {
    try {
        # Try to open with default browser
        Start-Process $htmlFile
        Write-Host "Application opened successfully!" -ForegroundColor Green
    }
    catch {
        Write-Host "Failed to open with default browser. Trying specific browsers..." -ForegroundColor Yellow
        
        # Try Chrome
        $chromePaths = @(
            "${env:ProgramFiles}\Google\Chrome\Application\chrome.exe",
            "${env:ProgramFiles(x86)}\Google\Chrome\Application\chrome.exe",
            "${env:LOCALAPPDATA}\Google\Chrome\Application\chrome.exe"
        )
        
        $chromeFound = $false
        foreach ($chromePath in $chromePaths) {
            if (Test-Path $chromePath) {
                try {
                    Start-Process $chromePath -ArgumentList $htmlFile
                    Write-Host "Opened with Chrome successfully!" -ForegroundColor Green
                    $chromeFound = $true
                    break
                }
                catch {
                    continue
                }
            }
        }
        
        if (-not $chromeFound) {
            # Try Edge
            try {
                Start-Process "msedge" -ArgumentList $htmlFile
                Write-Host "Opened with Edge successfully!" -ForegroundColor Green
            }
            catch {
                # Try Firefox
                try {
                    Start-Process "firefox" -ArgumentList $htmlFile
                    Write-Host "Opened with Firefox successfully!" -ForegroundColor Green
                }
                catch {
                    Write-Host "Could not open any browser. Please open the file manually:" -ForegroundColor Red
                    Write-Host $htmlFile -ForegroundColor Yellow
                }
            }
        }
    }
}
else {
    Write-Host "HTML file not found: $htmlFile" -ForegroundColor Red
}

# Keep window open for 3 seconds
Start-Sleep -Seconds 3
