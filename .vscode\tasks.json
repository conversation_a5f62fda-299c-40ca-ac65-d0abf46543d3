{"version": "2.0.0", "tasks": [{"label": "Open in <PERSON><PERSON><PERSON>er", "type": "shell", "command": "start", "args": ["${workspaceFolder}/ايكاروس الاهلية.html"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": [], "windows": {"command": "cmd", "args": ["/c", "start", "", "${workspaceFolder}/ايكاروس الاهلية.html"]}, "linux": {"command": "xdg-open", "args": ["${workspaceFolder}/ايكاروس الاهلية.html"]}, "osx": {"command": "open", "args": ["${workspaceFolder}/ايكاروس الاهلية.html"]}}, {"label": "Open in Chrome", "type": "shell", "command": "chrome", "args": ["${workspaceFolder}/ايكاروس الاهلية.html"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": [], "windows": {"command": "cmd", "args": ["/c", "start", "chrome", "${workspaceFolder}/ايكاروس الاهلية.html"]}}, {"label": "Start Live Server", "type": "shell", "command": "npx", "args": ["live-server", "--port=5500", "--open=${workspaceFolder}/ايكاروس الاهلية.html"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": [], "isBackground": true}]}