{"version": "0.2.0", "configurations": [{"name": "Open in Chrome", "type": "chrome", "request": "launch", "file": "${workspaceFolder}/ايكاروس الاهلية.html", "webRoot": "${workspaceFolder}", "runtimeExecutable": "chrome", "runtimeArgs": ["--allow-file-access-from-files", "--disable-web-security", "--user-data-dir=${workspaceFolder}/.chrome-user-data"]}, {"name": "Open in Edge", "type": "msedge", "request": "launch", "file": "${workspaceFolder}/ايكاروس الاهلية.html", "webRoot": "${workspaceFolder}", "runtimeArgs": ["--allow-file-access-from-files", "--disable-web-security"]}, {"name": "Open in Firefox", "type": "firefox", "request": "launch", "file": "${workspaceFolder}/ايكاروس الاهلية.html", "webRoot": "${workspaceFolder}"}, {"name": "Live Server", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/live-server/live-server.js", "args": ["--port=5500", "--host=localhost", "--open=${workspaceFolder}/ايكاروس الاهلية.html"], "console": "integratedTerminal"}]}