@echo off
echo إنشاء اختصار على سطح المكتب...

set "SCRIPT=%TEMP%\CreateShortcut.vbs"
set "DESKTOP=%USERPROFILE%\Desktop"
set "TARGET=%~dp0ايكاروس الاهلية.html"
set "SHORTCUT=%DESKTOP%\نظام ايكاروس الاهلية.lnk"

echo Set oWS = WScript.CreateObject("WScript.Shell") > "%SCRIPT%"
echo sLinkFile = "%SHORTCUT%" >> "%SCRIPT%"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%SCRIPT%"
echo oLink.TargetPath = "%TARGET%" >> "%SCRIPT%"
echo oLink.WorkingDirectory = "%~dp0" >> "%SCRIPT%"
echo oLink.Description = "نظام إدارة المشاريع - شركة ايكاروس الاهلية" >> "%SCRIPT%"
echo oLink.Save >> "%SCRIPT%"

cscript //nologo "%SCRIPT%"
del "%SCRIPT%"

echo ✅ تم إنشاء الاختصار على سطح المكتب بنجاح!
echo يمكنك الآن تشغيل التطبيق من سطح المكتب
pause
