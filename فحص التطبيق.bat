@echo off
title فحص حالة التطبيق
color 0B
echo.
echo ========================================
echo         فحص حالة التطبيق
echo ========================================
echo.

REM Check if HTML file exists
if exist "ايكاروس الاهلية.html" (
    echo ✅ ملف التطبيق موجود
    for %%A in ("ايكاروس الاهلية.html") do echo    الحجم: %%~zA بايت
    for %%A in ("ايكاروس الاهلية.html") do echo    تاريخ التعديل: %%~tA
) else (
    echo ❌ ملف التطبيق غير موجود
    goto :error
)

echo.
echo ========================================
echo         فحص المتصفحات المتاحة
echo ========================================
echo.

REM Check browsers
set "CHROME_FOUND=0"
set "EDGE_FOUND=0"
set "FIREFOX_FOUND=0"

if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    echo ✅ Google Chrome متاح
    set "CHROME_FOUND=1"
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    echo ✅ Google Chrome متاح
    set "CHROME_FOUND=1"
) else (
    echo ⚠️  Google Chrome غير مثبت
)

where msedge >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Microsoft Edge متاح
    set "EDGE_FOUND=1"
) else (
    echo ⚠️  Microsoft Edge غير متاح
)

where firefox >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Firefox متاح
    set "FIREFOX_FOUND=1"
) else (
    echo ⚠️  Firefox غير مثبت
)

echo.
echo ========================================
echo           ملخص الحالة
echo ========================================
echo.

if %CHROME_FOUND% equ 1 (
    echo ✅ التطبيق جاهز للعمل مع Chrome
) else if %EDGE_FOUND% equ 1 (
    echo ✅ التطبيق جاهز للعمل مع Edge
) else if %FIREFOX_FOUND% equ 1 (
    echo ✅ التطبيق جاهز للعمل مع Firefox
) else (
    echo ⚠️  لا توجد متصفحات مدعومة
    echo    يمكنك فتح الملف يدوياً
)

echo.
echo هل تريد تشغيل التطبيق الآن؟ (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    start "" "ايكاروس الاهلية.html"
    echo ✅ تم تشغيل التطبيق!
)

goto :end

:error
echo.
echo ❌ خطأ: لا يمكن العثور على ملف التطبيق
echo تأكد من وجود الملف في نفس المجلد
echo.

:end
echo.
pause
