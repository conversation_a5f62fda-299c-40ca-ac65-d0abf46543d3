/**
 * نظام التهيئة الشاملة لتطبيق إدارة المشاريع - شركة ايكاروس الاهلية
 * System Comprehensive Initialization
 */

class SystemInitializer {
    constructor() {
        this.isInitialized = false;
        this.components = {
            storage: false,
            ui: false,
            data: false,
            charts: false,
            forms: false,
            events: false
        };
        this.sampleData = [];
        this.initStartTime = Date.now();
    }

    /**
     * تهيئة شاملة للنظام
     */
    async initializeSystem() {
        console.log('🚀 بدء التهيئة الشاملة للنظام...');

        try {
            // 1. تهيئة التخزين المحلي
            await this.initializeStorage();

            // 2. تحميل البيانات الأولية
            await this.loadInitialData();

            // 3. تهيئة واجهة المستخدم
            await this.initializeUI();

            // 4. تفعيل الرسوم البيانية
            await this.initializeCharts();

            // 5. ربط النماذج والأحداث
            await this.initializeForms();

            // 6. تفعيل الأحداث التفاعلية
            await this.initializeEvents();

            // 7. التحقق النهائي
            await this.finalValidation();

            this.isInitialized = true;
            this.showInitializationComplete();

        } catch (error) {
            console.error('❌ خطأ في التهيئة:', error);
            this.showInitializationError(error);
        }
    }

    /**
     * تهيئة نظام التخزين المحلي
     */
    async initializeStorage() {
        console.log('📦 تهيئة نظام التخزين...');

        try {
            // فحص دعم التخزين المحلي
            if (typeof (Storage) === "undefined") {
                throw new Error("المتصفح لا يدعم التخزين المحلي");
            }

            // إنشاء مفاتيح التخزين
            const storageKeys = [
                'ikaros_projects',
                'ikaros_settings',
                'ikaros_user_preferences',
                'ikaros_backup'
            ];

            storageKeys.forEach(key => {
                if (!localStorage.getItem(key)) {
                    localStorage.setItem(key, JSON.stringify([]));
                }
            });

            // حفظ إعدادات النظام
            const systemSettings = {
                version: '1.0.0',
                lastInit: new Date().toISOString(),
                language: 'ar',
                theme: 'default',
                autoSave: true,
                backupEnabled: true
            };

            localStorage.setItem('ikaros_settings', JSON.stringify(systemSettings));

            this.components.storage = true;
            console.log('✅ تم تهيئة نظام التخزين بنجاح');

        } catch (error) {
            console.error('❌ فشل في تهيئة التخزين:', error);
            throw error;
        }
    }

    /**
     * تحميل البيانات الأولية
     */
    async loadInitialData() {
        console.log('📊 تحميل البيانات الأولية...');

        try {
            // بيانات تجريبية للمشاريع
            this.sampleData = [
                {
                    id: 'project_' + Date.now() + '_1',
                    name: 'مشروع برج الأعمال التجاري',
                    client: 'شركة التطوير العقاري',
                    value: 2500000,
                    status: 'جاري العمل',
                    progress: 65,
                    createdAt: { seconds: Date.now() / 1000 - 86400 * 30 },
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'project_' + Date.now() + '_2',
                    name: 'مجمع سكني الواحة',
                    client: 'مؤسسة الإسكان الحديث',
                    value: 1800000,
                    status: 'جاري العمل',
                    progress: 45,
                    createdAt: { seconds: Date.now() / 1000 - 86400 * 20 },
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'project_' + Date.now() + '_3',
                    name: 'مركز التسوق الكبير',
                    client: 'شركة الاستثمار التجاري',
                    value: 3200000,
                    status: 'مكتمل',
                    progress: 100,
                    createdAt: { seconds: Date.now() / 1000 - 86400 * 60 },
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'project_' + Date.now() + '_4',
                    name: 'فيلا سكنية فاخرة',
                    client: 'العميل الخاص',
                    value: 850000,
                    status: 'متوقف',
                    progress: 25,
                    createdAt: { seconds: Date.now() / 1000 - 86400 * 10 },
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'project_' + Date.now() + '_5',
                    name: 'مدرسة النور الابتدائية',
                    client: 'وزارة التربية والتعليم',
                    value: 1200000,
                    status: 'جاري العمل',
                    progress: 80,
                    createdAt: { seconds: Date.now() / 1000 - 86400 * 45 },
                    updatedAt: new Date().toISOString()
                }
            ];

            // فحص وجود بيانات سابقة
            const existingData = JSON.parse(localStorage.getItem('ikaros_projects') || '[]');

            if (existingData.length === 0) {
                // حفظ البيانات التجريبية
                localStorage.setItem('ikaros_projects', JSON.stringify(this.sampleData));
                console.log('📝 تم تحميل البيانات التجريبية');
            } else {
                this.sampleData = existingData;
                console.log('📂 تم تحميل البيانات الموجودة');
            }

            this.components.data = true;
            console.log('✅ تم تحميل البيانات بنجاح');

        } catch (error) {
            console.error('❌ فشل في تحميل البيانات:', error);
            throw error;
        }
    }

    /**
     * تهيئة واجهة المستخدم
     */
    async initializeUI() {
        console.log('🎨 تهيئة واجهة المستخدم...');

        try {
            // تحديث الإحصائيات
            this.updateDashboardStats();

            // تحديث جدول المشاريع
            this.updateProjectsTable();

            // تفعيل الرسوم المتحركة
            this.enableAnimations();

            // تحديث التاريخ والوقت
            this.updateDateTime();

            this.components.ui = true;
            console.log('✅ تم تهيئة واجهة المستخدم بنجاح');

        } catch (error) {
            console.error('❌ فشل في تهيئة واجهة المستخدم:', error);
            throw error;
        }
    }

    /**
     * تحديث إحصائيات لوحة التحكم
     */
    updateDashboardStats() {
        const totalProjects = this.sampleData.length;
        const ongoing = this.sampleData.filter(p => p.status === 'جاري العمل').length;
        const completed = this.sampleData.filter(p => p.status === 'مكتمل').length;
        const totalValue = this.sampleData.reduce((sum, p) => sum + (p.value || 0), 0);

        // تحديث العناصر مع تأثيرات متحركة
        this.animateNumber('stats-projects', totalProjects);
        this.animateNumber('stats-ongoing', ongoing);
        this.animateNumber('stats-completed', completed);

        const valueElement = document.getElementById('stats-value');
        if (valueElement) {
            valueElement.textContent = `${totalValue.toLocaleString('ar-KW')} د.ك`;
        }
    }

    /**
     * تحريك الأرقام
     */
    animateNumber(elementId, targetValue) {
        const element = document.getElementById(elementId);
        if (!element) return;

        const startValue = 0;
        const duration = 2000;
        const startTime = performance.now();

        const updateNumber = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const currentValue = Math.round(startValue + (targetValue - startValue) * this.easeOutCubic(progress));
            element.textContent = currentValue;

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        };

        requestAnimationFrame(updateNumber);
    }

    /**
     * دالة التسارع للحركة
     */
    easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }

    /**
     * تحديث جدول المشاريع
     */
    updateProjectsTable() {
        // سيتم استدعاء دالة renderProjects الموجودة في التطبيق الرئيسي
        if (typeof window.renderProjects === 'function') {
            window.renderProjects(this.sampleData);
        }
    }

    /**
     * تفعيل الرسوم المتحركة
     */
    enableAnimations() {
        // إضافة كلاسات CSS للحركة
        document.body.classList.add('system-initialized');

        // تأثيرات الظهور التدريجي
        const cards = document.querySelectorAll('.stats-card, .card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';

                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }, index * 200);
        });
    }

    /**
     * تحديث التاريخ والوقت
     */
    updateDateTime() {
        const updateTime = () => {
            const now = new Date();
            const options = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            const dateTimeString = now.toLocaleDateString('ar-SA', options);
            const dateTimeElement = document.getElementById('currentDateTime');
            if (dateTimeElement) {
                dateTimeElement.textContent = dateTimeString;
            }
        };

        updateTime();
        setInterval(updateTime, 60000); // تحديث كل دقيقة
    }

    /**
     * عرض رسالة اكتمال التهيئة
     */
    showInitializationComplete() {
        const duration = Date.now() - this.initStartTime;
        console.log(`🎉 تم اكتمال التهيئة الشاملة في ${duration}ms`);

        // عرض رسالة ترحيب
        if (typeof window.showToast === 'function') {
            setTimeout(() => {
                window.showToast('🎉 تم تهيئة النظام بنجاح! مرحباً بك في نظام إدارة المشاريع', 'success');
            }, 1000);
        }

        // تسجيل حالة التهيئة
        const initLog = {
            timestamp: new Date().toISOString(),
            duration: duration,
            components: this.components,
            dataCount: this.sampleData.length
        };

        localStorage.setItem('ikaros_last_init', JSON.stringify(initLog));
    }

    /**
     * عرض رسالة خطأ التهيئة
     */
    showInitializationError(error) {
        console.error('💥 فشل في التهيئة الشاملة:', error);

        if (typeof window.showToast === 'function') {
            window.showToast('❌ حدث خطأ في تهيئة النظام. يرجى إعادة تحميل الصفحة.', 'error');
        }
    }

    /**
     * تفعيل التحقق من صحة النماذج
     */
    enableFormValidation() {
        const validationRules = {
            projectName: { required: true, minLength: 3, maxLength: 100 },
            clientName: { required: true, minLength: 2, maxLength: 50 },
            projectValue: { required: true, min: 0, type: 'number' }
        };
        localStorage.setItem('ikaros_validation_rules', JSON.stringify(validationRules));
    }

    /**
     * تفعيل الحفظ التلقائي
     */
    enableAutoSave() {
        setInterval(() => {
            const currentData = JSON.parse(localStorage.getItem('ikaros_projects') || '[]');
            if (currentData.length > 0) {
                const backupData = {
                    timestamp: new Date().toISOString(),
                    data: currentData,
                    count: currentData.length
                };
                localStorage.setItem('ikaros_auto_backup', JSON.stringify(backupData));
                console.log('💾 تم الحفظ التلقائي للبيانات');
            }
        }, 30000);
    }

    /**
     * تفعيل اختصارات لوحة المفاتيح
     */
    enableKeyboardShortcuts() {
        const shortcuts = {
            'Ctrl+N': 'إضافة مشروع جديد',
            'Ctrl+S': 'حفظ',
            'Ctrl+P': 'طباعة',
            'F5': 'تحديث',
            'Escape': 'إغلاق النوافذ المنبثقة'
        };
        localStorage.setItem('ikaros_keyboard_shortcuts', JSON.stringify(shortcuts));
    }

    /**
     * تفعيل التفاعلات المتقدمة
     */
    enableAdvancedInteractions() {
        console.log('🎯 تم تفعيل التفاعلات المتقدمة');
    }

    /**
     * تفعيل النسخ الاحتياطي التلقائي
     */
    enableAutoBackup() {
        const createBackup = () => {
            const allData = {
                projects: JSON.parse(localStorage.getItem('ikaros_projects') || '[]'),
                settings: JSON.parse(localStorage.getItem('ikaros_settings') || '{}'),
                timestamp: new Date().toISOString()
            };
            const backupKey = `ikaros_backup_${new Date().toISOString().split('T')[0]}`;
            localStorage.setItem(backupKey, JSON.stringify(allData));
        };
        createBackup();
        setInterval(createBackup, 24 * 60 * 60 * 1000);
    }
}

// تصدير الكلاس للاستخدام العام
window.SystemInitializer = SystemInitializer;
