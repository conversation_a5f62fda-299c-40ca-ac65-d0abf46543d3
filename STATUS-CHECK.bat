@echo off
title Application Status Check
color 0B
cls
echo.
echo ==========================================
echo        APPLICATION STATUS CHECK
echo ==========================================
echo.

REM Check main HTML file
set "HTML_FOUND=0"
for %%f in (*.html) do (
    echo [✓] Main application file: FOUND - %%f
    for %%A in ("%%f") do echo     Size: %%~zA bytes
    set "HTML_FOUND=1"
    set "HTML_FILE=%%f"
)

if %HTML_FOUND% equ 0 (
    echo [✗] Main application file: NOT FOUND
    goto :error
)

REM Check logo file
if exist "b2e078f4-6153-41ef-8e10-1b75877307fe (1).jpg" (
    echo [✓] Logo file: FOUND
) else (
    echo [✗] Logo file: NOT FOUND
)

REM Check VS Code settings
if exist ".vscode\settings.json" (
    echo [✓] VS Code settings: CONFIGURED
) else (
    echo [✗] VS Code settings: NOT CONFIGURED
)

REM Check launch scripts
set "SCRIPTS_COUNT=0"
if exist "RUN-APP.bat" set /a SCRIPTS_COUNT+=1
if exist "start-app.cmd" set /a SCRIPTS_COUNT+=1
if exist "open-app.bat" set /a SCRIPTS_COUNT+=1

echo [✓] Launch scripts: %SCRIPTS_COUNT% available

echo.
echo ==========================================
echo           BROWSER CHECK
echo ==========================================
echo.

REM Check browsers
set "BROWSERS_FOUND=0"

where chrome >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Chrome: Available
    set /a BROWSERS_FOUND+=1
) else (
    if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
        echo [✓] Chrome: Available
        set /a BROWSERS_FOUND+=1
    ) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
        echo [✓] Chrome: Available  
        set /a BROWSERS_FOUND+=1
    ) else (
        echo [!] Chrome: Not installed
    )
)

where msedge >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Edge: Available
    set /a BROWSERS_FOUND+=1
) else (
    echo [!] Edge: Not available
)

where firefox >nul 2>&1
if %errorlevel% equ 0 (
    echo [✓] Firefox: Available
    set /a BROWSERS_FOUND+=1
) else (
    echo [!] Firefox: Not installed
)

echo.
echo ==========================================
echo            FINAL STATUS
echo ==========================================
echo.

if %BROWSERS_FOUND% gtr 0 (
    echo [✓] APPLICATION IS READY TO RUN
    echo     %BROWSERS_FOUND% browser^(s^) available
    echo.
    echo Available launch options:
    echo   1. Double-click: RUN-APP.bat
    echo   2. Double-click: start-app.cmd  
    echo   3. Drag HTML file to browser
    echo   4. In VS Code: Press F5
    echo.
    echo Would you like to start the application now? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo.
        echo Starting application...
        if defined HTML_FILE (
            start "" "%HTML_FILE%"
        ) else (
            for %%f in (*.html) do start "" "%%f"
        )
        echo [✓] Application launched successfully!
    )
) else (
    echo [!] WARNING: No browsers found
    echo     You can still open the HTML file manually
)

goto :end

:error
echo.
echo [✗] CRITICAL ERROR: Application files missing
echo     Please ensure all files are in the correct location
echo.

:end
echo.
echo Press any key to exit...
pause >nul
