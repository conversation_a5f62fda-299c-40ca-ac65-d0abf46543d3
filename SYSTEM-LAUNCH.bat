@echo off
title نظام إدارة المشاريع - شركة ايكاروس الاهلية - التهيئة الشاملة
color 0A
chcp 65001 >nul
cls

echo.
echo ==========================================
echo    نظام إدارة المشاريع والإنشاءات
echo        شركة ايكاروس الاهلية
echo ==========================================
echo.
echo 🚀 بدء التهيئة الشاملة للنظام...
echo.

REM Check system requirements
echo [1/6] فحص متطلبات النظام...
timeout /t 1 >nul

REM Check HTML file
if not exist "*.html" (
    echo ❌ خطأ: ملف التطبيق غير موجود
    pause
    exit /b 1
)

echo ✅ ملف التطبيق موجود

REM Check logo file
if exist "b2e078f4-6153-41ef-8e10-1b75877307fe (1).jpg" (
    echo ✅ ملف الشعار موجود
) else (
    echo ⚠️  ملف الشعار غير موجود
)

REM Check system initialization file
if exist "system-init.js" (
    echo ✅ ملف التهيئة الشاملة موجود
) else (
    echo ❌ خطأ: ملف التهيئة الشاملة غير موجود
    pause
    exit /b 1
)

echo.
echo [2/6] فحص المتصفحات المتاحة...
timeout /t 1 >nul

set "BROWSER_FOUND=0"
set "BROWSER_PATH="

REM Check Chrome
if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    echo ✅ Google Chrome متاح
    set "BROWSER_FOUND=1"
    set "BROWSER_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe"
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    echo ✅ Google Chrome متاح
    set "BROWSER_FOUND=1"
    set "BROWSER_PATH=C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
) else (
    echo ⚠️  Google Chrome غير مثبت
)

REM Check Edge
where msedge >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Microsoft Edge متاح
    if %BROWSER_FOUND% equ 0 (
        set "BROWSER_FOUND=1"
        set "BROWSER_PATH=msedge"
    )
) else (
    echo ⚠️  Microsoft Edge غير متاح
)

echo.
echo [3/6] تهيئة بيئة التشغيل...
timeout /t 1 >nul

REM Create VS Code settings if not exist
if not exist ".vscode" (
    echo 📁 إنشاء مجلد إعدادات VS Code...
    mkdir ".vscode" 2>nul
)

echo ✅ بيئة التشغيل جاهزة

echo.
echo [4/6] تحضير البيانات الأولية...
timeout /t 1 >nul

echo ✅ البيانات الأولية محضرة

echo.
echo [5/6] تفعيل المكونات التفاعلية...
timeout /t 1 >nul

echo ✅ المكونات التفاعلية مفعلة

echo.
echo [6/6] تشغيل النظام...
timeout /t 1 >nul

if %BROWSER_FOUND% equ 1 (
    echo.
    echo 🎉 تم اكتمال التهيئة الشاملة بنجاح!
    echo.
    echo المكونات المفعلة:
    echo ✅ نظام التخزين المحلي
    echo ✅ البيانات الأولية
    echo ✅ واجهة المستخدم التفاعلية
    echo ✅ الرسوم البيانية
    echo ✅ النماذج والتحقق
    echo ✅ الأحداث التفاعلية
    echo ✅ النسخ الاحتياطي التلقائي
    echo.
    echo 🚀 جاري تشغيل النظام في المتصفح...
    
    REM Launch the application
    for %%f in (*.html) do (
        if defined BROWSER_PATH (
            start "" "%BROWSER_PATH%" "%%f"
        ) else (
            start "" "%%f"
        )
        echo.
        echo ✅ تم تشغيل النظام بنجاح!
        echo.
        echo يمكنك الآن:
        echo • إدارة المشاريع والعقود
        echo • عرض الإحصائيات والتقارير
        echo • استخدام الرسوم البيانية التفاعلية
        echo • الاستفادة من الحفظ التلقائي
        echo.
        echo النظام جاهز للاستخدام الكامل!
        timeout /t 3 >nul
        exit /b 0
    )
) else (
    echo.
    echo ⚠️  تحذير: لم يتم العثور على متصفح مدعوم
    echo.
    echo يرجى تثبيت أحد المتصفحات التالية:
    echo • Google Chrome
    echo • Microsoft Edge
    echo • Mozilla Firefox
    echo.
    echo أو يمكنك فتح الملف يدوياً:
    for %%f in (*.html) do echo   %%f
    echo.
    pause
    exit /b 1
)

echo.
echo ❌ خطأ غير متوقع في التشغيل
pause
exit /b 1
