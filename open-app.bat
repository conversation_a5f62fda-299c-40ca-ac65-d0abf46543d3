@echo off
chcp 65001 >nul
echo Opening Ikaros Al-Ahliya Construction Management System...

REM Get the current directory
set "CURRENT_DIR=%~dp0"

REM Try to find and open the HTML file
for %%f in ("%CURRENT_DIR%*.html") do (
    echo Found HTML file: %%~nxf
    echo Opening with default browser...
    start "" "%%f"
    goto :success
)

REM If no HTML file found, try specific name
if exist "ايكاروس الاهلية.html" (
    start "" "ايكاروس الاهلية.html"
    goto :success
)

REM Try with Chrome
echo Trying Chrome...
"C:\Program Files\Google\Chrome\Application\chrome.exe" "%CURRENT_DIR%ايكاروس الاهلية.html" 2>nul
if %errorlevel% equ 0 goto :success

"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" "%CURRENT_DIR%ايكاروس الاهلية.html" 2>nul
if %errorlevel% equ 0 goto :success

REM Try with Edge
echo Trying Edge...
start msedge "%CURRENT_DIR%ايكاروس الاهلية.html" 2>nul
if %errorlevel% equ 0 goto :success

REM Try with Firefox
echo Trying Firefox...
start firefox "%CURRENT_DIR%ايكاروس الاهلية.html" 2>nul
if %errorlevel% equ 0 goto :success

echo Could not open browser automatically.
echo Please open the HTML file manually from this folder.
echo File location: %CURRENT_DIR%
pause
goto :end

:success
echo Application opened successfully!
timeout /t 2 >nul

:end
