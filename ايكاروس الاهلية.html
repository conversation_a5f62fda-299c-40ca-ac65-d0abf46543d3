<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شركة ايكاروس الاهلية - نظام إدارة الإنشاءات</title>
    <link rel="icon" type="image/x-icon" href="./b2e078f4-6153-41ef-8e10-1b75877307fe (1).jpg">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap"
        rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script
        src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

    <style>
        :root {
            --primary-color: #1e40af;
            --primary-dark: #1e3a8a;
            --secondary-color: #f59e0b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --light-color: #f8fafc;
            --dark-color: #1f2937;
            --sidebar-width: 280px;
            --sidebar-collapsed-width: 80px;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            width: var(--sidebar-width);
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar.collapsed .nav-text,
        .sidebar.collapsed .logo-text,
        .sidebar.collapsed .fa-chevron-down,
        .sidebar.collapsed .profile-info {
            display: none;
        }

        .main-content {
            transition: margin-right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-right: var(--sidebar-width);
        }

        .sidebar.collapsed~.main-content {
            margin-right: var(--sidebar-collapsed-width);
        }

        .nav-link {
            transition: all 0.3s ease;
            border-radius: 8px;
            margin: 4px 8px;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(-2px);
        }

        .nav-link.active-nav {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #f97316 100%);
            color: white !important;
            box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
        }

        .nav-link.active-nav i {
            color: white !important;
        }

        .dropdown-content {
            display: none;
            overflow: hidden;
            max-height: 0;
            transition: max-height 0.5s ease;
        }

        .dropdown.open .dropdown-content {
            display: block;
            max-height: 500px;
        }

        .dropdown .fa-chevron-down {
            transition: transform 0.3s ease;
        }

        .dropdown.open .fa-chevron-down {
            transform: rotate(180deg);
        }

        /* Enhanced Card Styles */
        .card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        /* Stats Cards */
        .stats-card {
            background: linear-gradient(135deg, white 0%, #f8fafc 100%);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1050;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(4px);
        }

        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: white;
            padding: 2.5rem;
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            transform: translateY(-50px) scale(0.9);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        }

        .modal-overlay.active .modal-content {
            transform: translateY(0) scale(1);
        }

        /* Enhanced Button Styles */
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #f97316 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
        }

        /* Toast Notification Styles */
        #toast-container {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1100;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .toast {
            padding: 1rem 1.5rem;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            opacity: 0;
            transform: translateY(-20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
        }

        .toast.show {
            opacity: 1;
            transform: translateY(0);
        }

        .toast.success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
        }

        .toast.error {
            background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
        }

        /* Enhanced Form Styles */
        .form-input {
            padding: 0.75rem 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .form-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
            outline: none;
        }

        /* Enhanced Table Styles */
        .table-row {
            transition: all 0.2s ease;
        }

        .table-row:hover {
            background-color: #f8fafc;
            transform: scale(1.01);
        }

        /* Loading Animation */
        .loading-spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        /* Progress Bar Styles */
        .progress-bar {
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            height: 8px;
            border-radius: 4px;
            transition: width 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }

            100% {
                transform: translateX(100%);
            }
        }

        /* Scrollbar Styles */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
        }

        /* Selection Styles */
        ::selection {
            background: rgba(30, 64, 175, 0.2);
            color: var(--primary-dark);
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                right: -280px;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                width: var(--sidebar-width);
            }

            .sidebar.open {
                right: 0;
            }

            .main-content {
                margin-right: 0 !important;
            }

            #toggleSidebar {
                display: none;
            }

            .controls-container {
                flex-direction: column;
                align-items: stretch;
            }

            .stats-card {
                padding: 1rem;
            }
        }

        #overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            backdrop-filter: blur(2px);
        }

        #overlay.active {
            display: block;
        }

        @media print {
            body * {
                visibility: hidden;
            }

            .printable,
            .printable * {
                visibility: visible;
            }

            .printable {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                margin: 0;
                padding: 0;
            }

            .sidebar,
            header,
            .non-printable {
                display: none !important;
            }

            .main-content {
                margin: 0 !important;
            }
        }
    </style>
</head>

<body class="bg-gray-100">
    <div id="toast-container"></div>
    <div class="flex">
        <!-- Sidebar -->
        <div id="sidebar" class="sidebar text-white h-screen fixed shadow-lg">
            <div class="p-6 flex items-center justify-between border-b border-white/20 h-20">
                <div class="flex items-center gap-3">
                    <img src="./b2e078f4-6153-41ef-8e10-1b75877307fe (1).jpg" alt="شركة ايكاروس الاهلية"
                        class="h-12 w-12 rounded-full object-cover border-2 border-white/30 logo-icon">
                    <div class="logo-text">
                        <h2 class="text-lg font-bold text-white">ايكاروس الاهلية</h2>
                        <p class="text-xs text-white/80">نظام إدارة الإنشاءات</p>
                    </div>
                </div>
                <button id="toggleSidebar"
                    class="text-white focus:outline-none hover:bg-white/10 p-2 rounded-full hidden md:block transition-all">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="p-4 border-b border-white/20 flex items-center">
                <div
                    class="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center ml-3 text-white font-bold">
                    ف.ع
                </div>
                <div class="profile-info">
                    <div class="font-semibold text-white">فهد العازمى</div>
                    <div class="text-xs text-white/70">مدير النظام</div>
                </div>
            </div>
            <nav class="mt-4">
                <a href="#" class="nav-link flex items-center p-3 text-blue-100 hover:bg-blue-700 active-nav">
                    <i class="fas fa-tachometer-alt ml-4 w-5 text-center"></i>
                    <span class="nav-text">لوحة التحكم</span>
                </a>
                <div class="dropdown">
                    <a href="#"
                        class="nav-link dropdown-toggle flex items-center justify-between p-3 text-blue-100 hover:bg-blue-700">
                        <div class="flex items-center">
                            <i class="fas fa-project-diagram ml-4 w-5 text-center"></i>
                            <span class="nav-text">المشاريع</span>
                        </div>
                        <i class="fas fa-chevron-down text-xs nav-text"></i>
                    </a>
                    <div class="dropdown-content bg-blue-900">
                        <a href="#" class="nav-link block p-3 pr-12 text-blue-100 hover:bg-blue-700">قائمة المشاريع</a>
                        <a href="#" class="nav-link block p-3 pr-12 text-blue-100 hover:bg-blue-700">إضافة مشروع</a>
                    </div>
                </div>
                <a href="#" class="nav-link flex items-center p-3 text-blue-100 hover:bg-blue-700">
                    <i class="fas fa-hard-hat ml-4 w-5 text-center"></i>
                    <span class="nav-text">العمال</span>
                </a>
                <a href="#" class="nav-link flex items-center p-3 text-blue-100 hover:bg-blue-700">
                    <i class="fas fa-tools ml-4 w-5 text-center"></i>
                    <span class="nav-text">المعدات</span>
                </a>
                <a href="#" class="nav-link flex items-center p-3 text-blue-100 hover:bg-blue-700">
                    <i class="fas fa-calculator ml-4 w-5 text-center"></i>
                    <span class="nav-text">الحسابات</span>
                </a>
                <a href="#" class="nav-link flex items-center p-3 text-blue-100 hover:bg-blue-700">
                    <i class="fas fa-cog ml-4 w-5 text-center"></i>
                    <span class="nav-text">الإعدادات</span>
                </a>
            </nav>
        </div>

        <!-- Enhanced Main Content -->
        <div class="main-content flex-1 min-h-screen">
            <header class="bg-white/80 backdrop-blur-lg shadow-lg sticky top-0 z-50 border-b border-gray-100">
                <div class="flex justify-between items-center p-6 h-20">
                    <div class="flex items-center gap-4">
                        <button id="mobileMenuButton"
                            class="md:hidden text-gray-600 focus:outline-none p-2 hover:bg-gray-100 rounded-lg transition-all">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-800">لوحة التحكم</h1>
                            <p class="text-sm text-gray-500" id="currentDateTime"></p>
                        </div>
                    </div>
                    <div class="flex items-center gap-3">
                        <div class="relative">
                            <button
                                class="p-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-all relative"
                                title="الإشعارات">
                                <i class="fas fa-bell text-lg"></i>
                                <span
                                    class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
                            </button>
                        </div>
                        <button
                            class="p-3 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-all"
                            title="الرسائل">
                            <i class="fas fa-envelope text-lg"></i>
                        </button>
                        <div class="w-px h-8 bg-gray-200"></div>
                        <div class="flex items-center gap-3">
                            <div class="text-right">
                                <div class="text-sm font-semibold text-gray-800">فهد العازمى</div>
                                <div class="text-xs text-gray-500">مدير النظام</div>
                            </div>
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                                ف.ع
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <main class="p-6">
                <!-- Enhanced Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 non-printable">
                    <div class="stats-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm font-medium">عدد المشاريع</p>
                                <h3 id="stats-projects" class="text-3xl font-bold text-gray-800 mt-2">0</h3>
                            </div>
                            <div
                                class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-project-diagram text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm font-medium">مشاريع جارية</p>
                                <h3 id="stats-ongoing" class="text-3xl font-bold text-green-600 mt-2">0</h3>
                            </div>
                            <div
                                class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-play-circle text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm font-medium">مشاريع مكتملة</p>
                                <h3 id="stats-completed" class="text-3xl font-bold text-blue-600 mt-2">0</h3>
                            </div>
                            <div
                                class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-check-circle text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                    <div class="stats-card">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm font-medium">إجمالي قيمة العقود</p>
                                <h3 id="stats-value" class="text-3xl font-bold text-purple-600 mt-2">0 د.ك</h3>
                            </div>
                            <div
                                class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-white text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Projects Table -->
                <div class="card overflow-hidden mb-8 printable">
                    <div
                        class="flex justify-between items-center p-6 border-b border-gray-100 flex-wrap gap-4 non-printable">
                        <div class="flex items-center gap-3">
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-list text-white"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-800">قائمة المشاريع</h2>
                        </div>
                        <div class="flex items-center gap-3 flex-wrap controls-container">
                            <div class="relative">
                                <input type="text" id="searchInput" placeholder="ابحث عن مشروع..."
                                    class="border border-gray-300 rounded-xl py-2.5 px-4 pr-10 text-sm w-full md:w-64 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                                <i
                                    class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                            <select id="statusFilter"
                                class="border border-gray-300 rounded-xl py-2.5 px-4 text-sm w-full md:w-auto focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">كل الحالات</option>
                                <option value="جاري العمل">جاري العمل</option>
                                <option value="متوقف">متوقف</option>
                                <option value="مكتمل">مكتمل</option>
                            </select>
                            <button id="addProjectBtn" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة مشروع
                            </button>
                            <button id="printBtn" class="btn bg-gray-600 text-white hover:bg-gray-700">
                                <i class="fas fa-print"></i> طباعة
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المشروع
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">العميل
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">القيمة
                                    </th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة
                                    </th>
                                    <th
                                        class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase non-printable">
                                        الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="projectsTableBody" class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td colspan="5" class="text-center py-10 text-gray-500">جاري تحميل البيانات... <i
                                            class="fas fa-spinner fa-spin ml-2"></i></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Enhanced Charts Section -->
                <div class="grid grid-cols-1 lg:grid-cols-5 gap-6 non-printable">
                    <div class="card p-6 lg:col-span-3">
                        <div class="flex items-center gap-3 mb-6">
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-chart-bar text-white"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-800">تقدم المشاريع (%)</h2>
                        </div>
                        <div class="h-80"><canvas id="progressChart"></canvas></div>
                    </div>
                    <div class="card p-6 lg:col-span-2">
                        <div class="flex items-center gap-3 mb-6">
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-chart-pie text-white"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-800">حالة المشاريع</h2>
                        </div>
                        <div class="h-80"><canvas id="statusChart"></canvas></div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Enhanced Project Modal -->
    <div id="projectModal" class="modal-overlay">
        <div class="modal-content">
            <div class="flex items-center gap-3 mb-6">
                <div
                    class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-project-diagram text-white text-lg"></i>
                </div>
                <h2 id="modalTitle" class="text-2xl font-bold text-gray-800">إضافة مشروع جديد</h2>
            </div>
            <form id="projectForm">
                <input type="hidden" id="projectId">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">اسم المشروع</label>
                        <input type="text" id="projectName"
                            class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            placeholder="أدخل اسم المشروع" required>
                    </div>
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">اسم العميل</label>
                        <input type="text" id="clientName"
                            class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            placeholder="أدخل اسم العميل" required>
                    </div>
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">قيمة العقد (د.ك)</label>
                        <input type="number" id="projectValue"
                            class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                            placeholder="0.00" required>
                    </div>
                    <div>
                        <label class="block text-sm font-semibold text-gray-700 mb-2">الحالة</label>
                        <select id="projectStatus"
                            class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                            <option>جاري العمل</option>
                            <option>متوقف</option>
                            <option>مكتمل</option>
                        </select>
                    </div>
                    <div class="md:col-span-2">
                        <label class="block text-sm font-semibold text-gray-700 mb-2">نسبة الإنجاز (%)</label>
                        <div class="flex items-center gap-4">
                            <input type="range" id="projectProgress" min="0" max="100" value="0"
                                class="flex-1 h-3 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                            <span id="progressValue" class="text-lg font-bold text-blue-600 min-w-[50px]">0%</span>
                        </div>
                    </div>
                </div>
                <div class="mt-8 flex justify-end gap-4">
                    <button type="button" id="cancelBtn" class="btn bg-gray-200 text-gray-800 hover:bg-gray-300">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" id="saveBtn" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- Enhanced Delete Modal -->
    <div id="deleteModal" class="modal-overlay">
        <div class="modal-content text-center">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-exclamation-triangle text-3xl text-red-500"></i>
            </div>
            <h2 class="text-2xl font-bold mb-4 text-gray-800">هل أنت متأكد؟</h2>
            <p class="text-gray-600 mb-8 text-lg">لا يمكن التراجع عن هذا الإجراء. سيتم حذف المشروع نهائياً.</p>
            <div class="flex justify-center gap-4">
                <button id="cancelDeleteBtn" class="btn bg-gray-200 text-gray-800 hover:bg-gray-300">
                    <i class="fas fa-times"></i> إلغاء
                </button>
                <button id="confirmDeleteBtn" class="btn bg-red-600 text-white hover:bg-red-700">
                    <i class="fas fa-trash"></i> نعم، احذف
                </button>
            </div>
        </div>
    </div>
    <div id="overlay"></div>

    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getAuth, signInAnonymously, signInWithCustomToken } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { getFirestore, enableIndexedDbPersistence, collection, onSnapshot, addDoc, doc, updateDoc, deleteDoc, getDoc, serverTimestamp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

        // Main function to initialize and run the application
        async function main() {
            // These variables are expected to be injected by the environment.
            const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : null;
            const appId = typeof __app_id !== 'undefined' ? __app_id : null;
            const authToken = typeof __initial_auth_token !== 'undefined' ? __initial_auth_token : null;

            // Verify that the config is available
            if (!firebaseConfig || !firebaseConfig.apiKey || !appId) {
                document.body.innerHTML = '<div class="text-center p-8 text-red-600 text-xl">تهيئة Firebase غير صالحة. لا يمكن تشغيل التطبيق.</div>';
                console.error("Firebase config or App ID is missing from the environment.");
                return;
            }

            try {
                // Initialize Firebase
                const app = initializeApp(firebaseConfig);
                const db = getFirestore(app);
                const auth = getAuth(app);

                // Enable offline persistence
                await enableIndexedDbPersistence(db);

                // Sign in with the provided token or anonymously
                if (authToken) {
                    await signInWithCustomToken(auth, authToken);
                } else {
                    await signInAnonymously(auth);
                }

                console.log("Firebase initialized successfully.");

                // Run the application logic, passing the db instance and appId
                runApp(db, appId);

            } catch (error) {
                console.error("Firebase Initialization Failed:", error);
                document.body.innerHTML = `<div class="text-center p-8 text-red-600">فشل الاتصال بقاعدة البيانات. تأكد من صحة إعدادات Firebase. <br><small>${error.message}</small></div>`;
            }
        }

        // This function contains the entire application logic.
        function runApp(db, appId) {
            // --- DOM Elements ---
            const tableBody = document.getElementById('projectsTableBody');
            const addProjectBtn = document.getElementById('addProjectBtn');
            const projectModal = document.getElementById('projectModal');
            const deleteModal = document.getElementById('deleteModal');
            const cancelBtn = document.getElementById('cancelBtn');
            const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
            const projectForm = document.getElementById('projectForm');
            const progressSlider = document.getElementById('projectProgress');
            const progressValue = document.getElementById('progressValue');
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');

            let currentProjectIdToDelete = null;
            let progressChart, statusChart;
            let allProjects = []; // Cache for projects
            const LOCAL_STORAGE_KEY = `ikaros_projects_${appId}`; // مفتاح فريد للتخزين المحلي

            // --- Enhanced Toast Notifications ---
            function showToast(message, type = 'success') {
                const toastContainer = document.getElementById('toast-container');
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;

                // Add icon based on type
                const icon = type === 'success' ? 'fas fa-check-circle' :
                    type === 'error' ? 'fas fa-exclamation-circle' :
                        'fas fa-info-circle';

                toast.innerHTML = `<i class="${icon} ml-2"></i>${message}`;
                toastContainer.appendChild(toast);

                // Add sound effect (optional)
                if (type === 'success') {
                    // You can add a success sound here
                } else if (type === 'error') {
                    // You can add an error sound here
                }

                setTimeout(() => { toast.classList.add('show'); }, 10);
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => toast.remove(), 500);
                }, 4000);
            }

            // --- Form Validation ---
            function validateProjectForm() {
                const name = document.getElementById('projectName').value.trim();
                const client = document.getElementById('clientName').value.trim();
                const value = document.getElementById('projectValue').value;

                if (!name) {
                    showToast('يرجى إدخال اسم المشروع', 'error');
                    return false;
                }
                if (!client) {
                    showToast('يرجى إدخال اسم العميل', 'error');
                    return false;
                }
                if (!value || value <= 0) {
                    showToast('يرجى إدخال قيمة صحيحة للعقد', 'error');
                    return false;
                }
                return true;
            }

            // --- Loading State Management ---
            function setLoadingState(element, isLoading) {
                if (isLoading) {
                    element.disabled = true;
                    const originalText = element.innerHTML;
                    element.setAttribute('data-original-text', originalText);
                    element.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري المعالجة...';
                } else {
                    element.disabled = false;
                    element.innerHTML = element.getAttribute('data-original-text');
                }
            }

            // --- Modal Controls ---
            function openModal() { projectModal.classList.add('active'); }
            function closeModal() { projectModal.classList.remove('active'); projectForm.reset(); document.getElementById('projectId').value = ''; progressValue.textContent = '0%'; }
            function openDeleteModal(id) { currentProjectIdToDelete = id; deleteModal.classList.add('active'); }
            function closeDeleteModal() { deleteModal.classList.remove('active'); }

            addProjectBtn.addEventListener('click', () => {
                document.getElementById('modalTitle').textContent = 'إضافة مشروع جديد';
                openModal();
            });
            cancelBtn.addEventListener('click', closeModal);
            cancelDeleteBtn.addEventListener('click', closeDeleteModal);

            // --- تحميل البيانات من التخزين المحلي عند بدء التشغيل ---
            function loadFromLocalStorage() {
                const storedProjects = localStorage.getItem(LOCAL_STORAGE_KEY);
                if (storedProjects) {
                    try {
                        allProjects = JSON.parse(storedProjects);
                        console.log("تم تحميل المشاريع من التخزين المحلي.");
                        renderProjects(allProjects);
                        updateDashboard(allProjects);
                        updateCharts(allProjects);
                        filterTable();
                    } catch (e) {
                        console.error("خطأ في قراءة البيانات من التخزين المحلي", e);
                        localStorage.removeItem(LOCAL_STORAGE_KEY); // حذف البيانات التالفة
                    }
                }
            }

            loadFromLocalStorage(); // استدعاء الدالة عند بدء تشغيل التطبيق

            // --- Real-time Data Listener ---
            const projectsCollectionPath = `artifacts/${appId}/public/data/projects`;
            const projectsCollection = collection(db, projectsCollectionPath);
            onSnapshot(projectsCollection, (snapshot) => {
                allProjects = [];
                snapshot.forEach(doc => allProjects.push({ id: doc.id, ...doc.data() }));

                // --- حفظ البيانات في التخزين المحلي بعد كل تحديث من Firebase ---
                try {
                    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(allProjects));
                } catch (e) {
                    console.error("خطأ في حفظ البيانات في التخزين المحلي", e);
                }

                renderProjects(allProjects);
                updateDashboard(allProjects);
                updateCharts(allProjects);
                filterTable();
            }, (error) => {
                console.error("Error fetching projects: ", error);
                showToast("فشل الاتصال بالخادم، يتم عرض البيانات المحلية.", "error");
                // لا تقم بمسح الجدول إذا فشل الاتصال، احتفظ بالبيانات المحلية
            });

            // --- Render Functions ---
            // --- Enhanced Render Functions ---
            function renderProjects(projects) {
                tableBody.innerHTML = '';
                if (projects.length === 0) {
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="5" class="text-center py-16">
                                <div class="flex flex-col items-center">
                                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                        <i class="fas fa-folder-open text-gray-400 text-2xl"></i>
                                    </div>
                                    <p class="text-gray-500 text-lg font-medium">لا توجد مشاريع لعرضها</p>
                                    <p class="text-gray-400 text-sm mt-1">ابدأ بإضافة مشروع جديد</p>
                                </div>
                            </td>
                        </tr>`;
                    return;
                }

                const sortedProjects = projects.sort((a, b) => (b.createdAt?.seconds || 0) - (a.createdAt?.seconds || 0));

                sortedProjects.forEach(p => {
                    const statusColors = {
                        'جاري العمل': 'bg-green-100 text-green-800 border-green-200',
                        'متوقف': 'bg-yellow-100 text-yellow-800 border-yellow-200',
                        'مكتمل': 'bg-blue-100 text-blue-800 border-blue-200'
                    };
                    const statusIcons = {
                        'جاري العمل': 'fas fa-play-circle',
                        'متوقف': 'fas fa-pause-circle',
                        'مكتمل': 'fas fa-check-circle'
                    };

                    const progressWidth = p.progress || 0;
                    const row = `
                        <tr data-id="${p.id}" class="table-row hover:bg-gray-50 transition-all">
                            <td class="px-6 py-4">
                                <div class="flex flex-col">
                                    <div class="font-semibold text-gray-900">${p.name || ''}</div>
                                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                                        <div class="progress-bar h-2 rounded-full" style="width: ${progressWidth}%"></div>
                                    </div>
                                    <span class="text-xs text-gray-500 mt-1">${progressWidth}% مكتمل</span>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center ml-3">
                                        <i class="fas fa-user text-gray-500 text-sm"></i>
                                    </div>
                                    <span class="text-sm font-medium text-gray-700">${p.client || ''}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-bold text-gray-900">${Number(p.value || 0).toLocaleString('ar-KW')} د.ك</div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full border ${statusColors[p.status] || 'bg-gray-100 border-gray-200'}">
                                    <i class="${statusIcons[p.status] || 'fas fa-circle'} ml-1"></i>
                                    ${p.status || ''}
                                </span>
                            </td>
                            <td class="px-6 py-4 non-printable">
                                <div class="flex items-center gap-2">
                                    <button class="edit-btn p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="delete-btn p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>`;
                    tableBody.insertAdjacentHTML('beforeend', row);
                });
            }

            function updateDashboard(projects) {
                const totalProjects = projects.length;
                const ongoing = projects.filter(p => p.status === 'جاري العمل').length;
                const completed = projects.filter(p => p.status === 'مكتمل').length;
                const paused = projects.filter(p => p.status === 'متوقف').length;
                const totalValue = projects.reduce((sum, p) => sum + Number(p.value || 0), 0);

                // Calculate average progress
                const avgProgress = projects.length > 0 ?
                    Math.round(projects.reduce((sum, p) => sum + (p.progress || 0), 0) / projects.length) : 0;

                // Animate numbers
                animateNumber('stats-projects', totalProjects);
                animateNumber('stats-ongoing', ongoing);
                animateNumber('stats-completed', completed);

                document.getElementById('stats-value').textContent = `${totalValue.toLocaleString('ar-KW')} د.ك`;
            }

            // --- Number Animation Function ---
            function animateNumber(elementId, targetValue) {
                const element = document.getElementById(elementId);
                const startValue = parseInt(element.textContent) || 0;
                const duration = 1000; // 1 second
                const startTime = performance.now();

                function updateNumber(currentTime) {
                    const elapsed = currentTime - startTime;
                    const progress = Math.min(elapsed / duration, 1);
                    const currentValue = Math.round(startValue + (targetValue - startValue) * progress);
                    element.textContent = currentValue;

                    if (progress < 1) {
                        requestAnimationFrame(updateNumber);
                    }
                }

                requestAnimationFrame(updateNumber);
            }

            function updateCharts(projects) {
                const progressCtx = document.getElementById('progressChart').getContext('2d');
                const statusCtx = document.getElementById('statusChart').getContext('2d');

                // Enhanced Progress Chart
                if (progressChart) progressChart.destroy();
                progressChart = new Chart(progressCtx, {
                    type: 'bar',
                    data: {
                        labels: projects.map(p => p.name?.length > 20 ? p.name.substring(0, 20) + '...' : p.name),
                        datasets: [{
                            label: 'نسبة الإنجاز (%)',
                            data: projects.map(p => p.progress || 0),
                            backgroundColor: projects.map(p => {
                                const progress = p.progress || 0;
                                if (progress >= 80) return 'rgba(16, 185, 129, 0.8)'; // Green
                                if (progress >= 50) return 'rgba(245, 158, 11, 0.8)'; // Yellow
                                return 'rgba(239, 68, 68, 0.8)'; // Red
                            }),
                            borderColor: projects.map(p => {
                                const progress = p.progress || 0;
                                if (progress >= 80) return 'rgba(16, 185, 129, 1)';
                                if (progress >= 50) return 'rgba(245, 158, 11, 1)';
                                return 'rgba(239, 68, 68, 1)';
                            }),
                            borderWidth: 2,
                            borderRadius: 8,
                            borderSkipped: false,
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        indexAxis: 'y',
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top',
                                labels: {
                                    font: {
                                        family: 'Cairo',
                                        size: 12
                                    }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleFont: { family: 'Cairo' },
                                bodyFont: { family: 'Cairo' },
                                callbacks: {
                                    label: function (context) {
                                        return `${context.parsed.x}% مكتمل`;
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                beginAtZero: true,
                                max: 100,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.1)'
                                },
                                ticks: {
                                    font: { family: 'Cairo' },
                                    callback: function (value) {
                                        return value + '%';
                                    }
                                }
                            },
                            y: {
                                grid: {
                                    display: false
                                },
                                ticks: {
                                    font: { family: 'Cairo' }
                                }
                            }
                        }
                    }
                });

                // Enhanced Status Chart
                const statusCounts = projects.reduce((acc, p) => { acc[p.status] = (acc[p.status] || 0) + 1; return acc; }, {});
                if (statusChart) statusChart.destroy();
                statusChart = new Chart(statusCtx, {
                    type: 'doughnut',
                    data: {
                        labels: Object.keys(statusCounts),
                        datasets: [{
                            data: Object.values(statusCounts),
                            backgroundColor: [
                                'rgba(16, 185, 129, 0.8)', // Green for جاري العمل
                                'rgba(245, 158, 11, 0.8)', // Yellow for متوقف
                                'rgba(59, 130, 246, 0.8)', // Blue for مكتمل
                                'rgba(107, 114, 128, 0.8)'  // Gray for others
                            ],
                            borderColor: [
                                'rgba(16, 185, 129, 1)',
                                'rgba(245, 158, 11, 1)',
                                'rgba(59, 130, 246, 1)',
                                'rgba(107, 114, 128, 1)'
                            ],
                            borderWidth: 3,
                            hoverOffset: 10
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    font: {
                                        family: 'Cairo',
                                        size: 12
                                    },
                                    padding: 20,
                                    usePointStyle: true,
                                    pointStyle: 'circle'
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleFont: { family: 'Cairo' },
                                bodyFont: { family: 'Cairo' },
                                callbacks: {
                                    label: function (context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                                        return `${context.label}: ${context.parsed} (${percentage}%)`;
                                    }
                                }
                            }
                        }
                    }
                });
            }

            // --- Enhanced CRUD Operations ---
            projectForm.addEventListener('submit', async (e) => {
                e.preventDefault();

                // Validate form before submission
                if (!validateProjectForm()) {
                    return;
                }

                const saveBtn = document.getElementById('saveBtn');
                setLoadingState(saveBtn, true);

                const id = document.getElementById('projectId').value;
                const projectData = {
                    name: document.getElementById('projectName').value.trim(),
                    client: document.getElementById('clientName').value.trim(),
                    value: parseFloat(document.getElementById('projectValue').value) || 0,
                    status: document.getElementById('projectStatus').value,
                    progress: parseInt(document.getElementById('projectProgress').value) || 0,
                    updatedAt: serverTimestamp()
                };

                try {
                    if (id) {
                        await updateDoc(doc(db, projectsCollectionPath, id), projectData);
                        showToast("تم تحديث المشروع بنجاح!", "success");
                    } else {
                        projectData.createdAt = serverTimestamp();
                        await addDoc(collection(db, projectsCollectionPath), projectData);
                        showToast("تمت إضافة المشروع بنجاح!", "success");
                    }
                    closeModal();
                } catch (error) {
                    console.error("Error saving project: ", error);
                    showToast("حدث خطأ أثناء الحفظ. يرجى المحاولة مرة أخرى.", "error");
                } finally {
                    setLoadingState(saveBtn, false);
                }
            });

            tableBody.addEventListener('click', async (e) => {
                const target = e.target.closest('button');
                if (!target) return;
                const row = target.closest('tr');
                const id = row.dataset.id;
                if (target.classList.contains('edit-btn')) {
                    const docRef = doc(db, projectsCollectionPath, id);
                    const docSnap = await getDoc(docRef);
                    if (docSnap.exists()) {
                        const data = docSnap.data();
                        document.getElementById('modalTitle').textContent = 'تعديل مشروع';
                        document.getElementById('projectId').value = id;
                        document.getElementById('projectName').value = data.name;
                        document.getElementById('clientName').value = data.client;
                        document.getElementById('projectValue').value = data.value;
                        document.getElementById('projectStatus').value = data.status;
                        progressSlider.value = data.progress;
                        progressValue.textContent = `${data.progress}%`;
                        openModal();
                    }
                } else if (target.classList.contains('delete-btn')) {
                    openDeleteModal(id);
                }
            });

            document.getElementById('confirmDeleteBtn').addEventListener('click', async () => {
                if (currentProjectIdToDelete) {
                    try {
                        await deleteDoc(doc(db, projectsCollectionPath, currentProjectIdToDelete));
                        showToast("تم حذف المشروع بنجاح!");
                        closeDeleteModal();
                    } catch (error) {
                        console.error("Error deleting project: ", error);
                        showToast("حدث خطأ أثناء الحذف", "error");
                    }
                }
            });

            // --- UI Helpers & Filtering ---
            progressSlider.addEventListener('input', (e) => { progressValue.textContent = `${e.target.value}%`; });
            function filterTable() {
                const searchValue = searchInput.value.toLowerCase().trim();
                const statusValue = statusFilter.value;
                const rows = tableBody.getElementsByTagName('tr');
                let visibleRows = 0;
                for (let row of rows) {
                    if (row.getElementsByTagName('td').length === 1) continue;
                    const statusText = row.querySelector('td:nth-child(4) span').textContent.trim();
                    const rowText = row.textContent.toLowerCase();
                    const searchMatch = rowText.includes(searchValue);
                    const statusMatch = statusValue === "" || statusText === statusValue;
                    if (searchMatch && statusMatch) {
                        row.style.display = "";
                        visibleRows++;
                    } else {
                        row.style.display = "none";
                    }
                }
                const noProjectsRow = tableBody.querySelector('td[colspan="5"]');
                if (noProjectsRow) {
                    if (allProjects.length === 0) noProjectsRow.parentElement.style.display = '';
                    else noProjectsRow.parentElement.style.display = visibleRows === 0 ? '' : 'none';
                }
            }
            searchInput.addEventListener('keyup', filterTable);
            statusFilter.addEventListener('change', filterTable);
            document.getElementById('printBtn').addEventListener('click', () => window.print());

            // --- Enhanced UI Features ---

            // Update current date and time
            function updateDateTime() {
                const now = new Date();
                const options = {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                };
                const dateTimeString = now.toLocaleDateString('ar-SA', options);
                const dateTimeElement = document.getElementById('currentDateTime');
                if (dateTimeElement) {
                    dateTimeElement.textContent = dateTimeString;
                }
            }

            // Update time every minute
            updateDateTime();
            setInterval(updateDateTime, 60000);

            // --- Sidebar Logic ---
            const sidebar = document.getElementById('sidebar');
            document.getElementById('toggleSidebar').addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
                // Save sidebar state to localStorage
                localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
            });

            // Restore sidebar state
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
            }

            document.getElementById('mobileMenuButton').addEventListener('click', (e) => {
                e.stopPropagation();
                sidebar.classList.add('open');
                document.getElementById('overlay').classList.add('active');
            });

            document.getElementById('overlay').addEventListener('click', () => {
                sidebar.classList.remove('open');
                document.getElementById('overlay').classList.remove('active');
            });

            // --- Keyboard Shortcuts ---
            document.addEventListener('keydown', (e) => {
                // Ctrl/Cmd + N for new project
                if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                    e.preventDefault();
                    document.getElementById('modalTitle').textContent = 'إضافة مشروع جديد';
                    openModal();
                }

                // Escape to close modals
                if (e.key === 'Escape') {
                    closeModal();
                    closeDeleteModal();
                }
            });

            // --- Welcome Animation ---
            setTimeout(() => {
                showToast('مرحباً بك في نظام إدارة المشاريع', 'success');
            }, 1000);
        }

        // Run the main function when the DOM is ready
        document.addEventListener('DOMContentLoaded', main);
    </script>
</body>

</html>