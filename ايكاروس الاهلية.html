<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شركة ايكاروس الاهلية - نظام إدارة الإنشاءات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .sidebar { transition: all 0.3s ease-in-out; width: 250px; }
        .sidebar.collapsed { width: 80px; }
        .sidebar.collapsed .nav-text, .sidebar.collapsed .logo-text, .sidebar.collapsed .fa-chevron-down, .sidebar.collapsed .profile-info { display: none; }
        .main-content { transition: margin-right 0.3s ease-in-out; margin-right: 250px; }
        .sidebar.collapsed ~ .main-content { margin-right: 80px; }
        .nav-link.active-nav { background-color: #1d4ed8; color: white !important; border-right: 4px solid #fbbf24; }
        .nav-link.active-nav i { color: white !important; }
        .dropdown-content { display: none; overflow: hidden; max-height: 0; transition: max-height 0.5s ease; }
        .dropdown.open .dropdown-content { display: block; max-height: 500px; }
        .dropdown .fa-chevron-down { transition: transform 0.3s ease; }
        .dropdown.open .fa-chevron-down { transform: rotate(180deg); }

        /* Modal Styles */
        .modal-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 1050; opacity: 0; visibility: hidden; transition: all 0.3s ease; }
        .modal-overlay.active { opacity: 1; visibility: visible; }
        .modal-content { background: white; padding: 2rem; border-radius: 0.5rem; width: 90%; max-width: 500px; transform: translateY(-50px); transition: all 0.3s ease; }
        .modal-overlay.active .modal-content { transform: translateY(0); }
        
        /* Toast Notification Styles */
        #toast-container { position: fixed; top: 20px; left: 50%; transform: translateX(-50%); z-index: 1100; display: flex; flex-direction: column; gap: 10px; }
        .toast { padding: 1rem 1.5rem; border-radius: 0.5rem; color: white; font-weight: bold; opacity: 0; transform: translateY(-20px); transition: all 0.4s ease; }
        .toast.show { opacity: 1; transform: translateY(0); }
        .toast.success { background-color: #16a34a; }
        .toast.error { background-color: #dc2626; }

        @media (max-width: 768px) {
            .sidebar { position: fixed; right: -250px; top: 0; height: 100vh; z-index: 1000; transition: right 0.3s ease-in-out; }
            .sidebar.open { right: 0; }
            .main-content { margin-right: 0 !important; }
            #toggleSidebar { display: none; }
            .controls-container { flex-direction: column; align-items: stretch; }
        }
        #overlay { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 999; }
        #overlay.active { display: block; }
        @media print {
            body * { visibility: hidden; }
            .printable, .printable * { visibility: visible; }
            .printable { position: absolute; left: 0; top: 0; width: 100%; margin: 0; padding: 0; }
            .sidebar, header, .non-printable { display: none !important; }
            .main-content { margin: 0 !important; }
        }
    </style>
</head>
<body class="bg-gray-100">
    <div id="toast-container"></div>
    <div class="flex">
        <!-- Sidebar -->
        <div id="sidebar" class="sidebar bg-blue-800 text-white h-screen fixed shadow-lg">
            <div class="p-4 flex items-center justify-between border-b border-blue-700 h-16">
                <div class="flex items-center">
                    <img src="https://placehold.co/150x50/1d4ed8/FFFFFF?text=IKAROS&font=cairo" alt="IKAROS Logo" class="h-12 w-auto object-contain logo-text">
                </div>
                <button id="toggleSidebar" class="text-white focus:outline-none hover:bg-blue-700 p-2 rounded-full hidden md:block">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="p-4 border-b border-blue-700 flex items-center">
                <img src="https://placehold.co/40x40/E2E8F0/334155?text=FA" alt="Profile" class="rounded-full ml-3">
                <div class="profile-info">
                    <div class="font-medium">فهد العازمى</div>
                    <div class="text-xs text-blue-200">مدير النظام</div>
                </div>
            </div>
            <nav class="mt-4">
                <a href="#" class="nav-link flex items-center p-3 text-blue-100 hover:bg-blue-700 active-nav">
                    <i class="fas fa-tachometer-alt ml-4 w-5 text-center"></i>
                    <span class="nav-text">لوحة التحكم</span>
                </a>
                <div class="dropdown">
                    <a href="#" class="nav-link dropdown-toggle flex items-center justify-between p-3 text-blue-100 hover:bg-blue-700">
                        <div class="flex items-center">
                            <i class="fas fa-project-diagram ml-4 w-5 text-center"></i>
                            <span class="nav-text">المشاريع</span>
                        </div>
                        <i class="fas fa-chevron-down text-xs nav-text"></i>
                    </a>
                    <div class="dropdown-content bg-blue-900">
                        <a href="#" class="nav-link block p-3 pr-12 text-blue-100 hover:bg-blue-700">قائمة المشاريع</a>
                        <a href="#" class="nav-link block p-3 pr-12 text-blue-100 hover:bg-blue-700">إضافة مشروع</a>
                    </div>
                </div>
                <a href="#" class="nav-link flex items-center p-3 text-blue-100 hover:bg-blue-700">
                    <i class="fas fa-hard-hat ml-4 w-5 text-center"></i>
                    <span class="nav-text">العمال</span>
                </a>
                <a href="#" class="nav-link flex items-center p-3 text-blue-100 hover:bg-blue-700">
                    <i class="fas fa-tools ml-4 w-5 text-center"></i>
                    <span class="nav-text">المعدات</span>
                </a>
                <a href="#" class="nav-link flex items-center p-3 text-blue-100 hover:bg-blue-700">
                    <i class="fas fa-calculator ml-4 w-5 text-center"></i>
                    <span class="nav-text">الحسابات</span>
                </a>
                <a href="#" class="nav-link flex items-center p-3 text-blue-100 hover:bg-blue-700">
                    <i class="fas fa-cog ml-4 w-5 text-center"></i>
                    <span class="nav-text">الإعدادات</span>
                </a>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="main-content flex-1 min-h-screen">
            <header class="bg-white shadow-sm sticky top-0 z-50">
                <div class="flex justify-between items-center p-4 h-16">
                    <div class="flex items-center">
                        <button id="mobileMenuButton" class="md:hidden text-gray-600 focus:outline-none ml-4">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <h1 class="text-xl font-bold text-gray-800">لوحة التحكم</h1>
                    </div>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <button class="text-gray-600 focus:outline-none hover:text-blue-600"><i class="fas fa-bell text-xl"></i></button>
                        <button class="text-gray-600 focus:outline-none hover:text-blue-600"><i class="fas fa-envelope text-xl"></i></button>
                    </div>
                </div>
            </header>
            
            <main class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6 non-printable">
                    <div class="bg-white rounded-lg shadow p-6"><p class="text-gray-500">عدد المشاريع</p><h3 id="stats-projects" class="text-2xl font-bold text-gray-800">0</h3></div>
                    <div class="bg-white rounded-lg shadow p-6"><p class="text-gray-500">مشاريع جارية</p><h3 id="stats-ongoing" class="text-2xl font-bold text-green-600">0</h3></div>
                    <div class="bg-white rounded-lg shadow p-6"><p class="text-gray-500">مشاريع مكتملة</p><h3 id="stats-completed" class="text-2xl font-bold text-blue-600">0</h3></div>
                    <div class="bg-white rounded-lg shadow p-6"><p class="text-gray-500">إجمالي قيمة العقود</p><h3 id="stats-value" class="text-2xl font-bold text-purple-600">0 د.ك</h3></div>
                </div>
                
                <div class="bg-white rounded-lg shadow overflow-hidden mb-6 printable">
                    <div class="flex justify-between items-center p-4 border-b flex-wrap gap-4 non-printable">
                        <h2 class="text-lg font-bold text-gray-800">قائمة المشاريع</h2>
                        <div class="flex items-center gap-2 flex-wrap controls-container">
                            <div class="relative"><input type="text" id="searchInput" placeholder="ابحث..." class="border rounded-lg py-2 px-3 pr-10 text-sm w-full md:w-auto"> <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i></div>
                            <select id="statusFilter" class="border rounded-lg py-2 px-3 text-sm w-full md:w-auto"><option value="">كل الحالات</option><option value="جاري العمل">جاري العمل</option><option value="متوقف">متوقف</option><option value="مكتمل">مكتمل</option></select>
                            <button id="addProjectBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-bold hover:bg-blue-700 flex items-center gap-2"><i class="fas fa-plus"></i> إضافة مشروع</button>
                            <button id="printBtn" class="bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-bold hover:bg-gray-700 flex items-center gap-2"><i class="fas fa-print"></i> طباعة</button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50"><tr><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المشروع</th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">العميل</th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">القيمة</th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th><th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase non-printable">الإجراءات</th></tr></thead>
                            <tbody id="projectsTableBody" class="bg-white divide-y divide-gray-200"><tr><td colspan="5" class="text-center py-10 text-gray-500">جاري تحميل البيانات... <i class="fas fa-spinner fa-spin ml-2"></i></td></tr></tbody>
                        </table>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-5 gap-6 non-printable">
                    <div class="bg-white rounded-lg shadow p-6 lg:col-span-3"><h2 class="text-lg font-bold text-gray-800 mb-4">تقدم المشاريع (%)</h2><div class="h-80"><canvas id="progressChart"></canvas></div></div>
                    <div class="bg-white rounded-lg shadow p-6 lg:col-span-2"><h2 class="text-lg font-bold text-gray-800 mb-4">حالة المشاريع</h2><div class="h-80"><canvas id="statusChart"></canvas></div></div>
                </div>
            </main>
        </div>
    </div>
    
    <div id="projectModal" class="modal-overlay"><div class="modal-content"><h2 id="modalTitle" class="text-xl font-bold mb-4">إضافة مشروع جديد</h2><form id="projectForm"><input type="hidden" id="projectId"><div class="grid grid-cols-1 md:grid-cols-2 gap-4"><div><label class="block text-sm font-medium text-gray-700">اسم المشروع</label><input type="text" id="projectName" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required></div><div><label class="block text-sm font-medium text-gray-700">اسم العميل</label><input type="text" id="clientName" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required></div><div><label class="block text-sm font-medium text-gray-700">قيمة العقد (د.ك)</label><input type="number" id="projectValue" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required></div><div><label class="block text-sm font-medium text-gray-700">الحالة</label><select id="projectStatus" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"><option>جاري العمل</option><option>متوقف</option><option>مكتمل</option></select></div><div class="md:col-span-2"><label class="block text-sm font-medium text-gray-700">نسبة الإنجاز (%)</label><input type="range" id="projectProgress" min="0" max="100" value="0" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"><span id="progressValue" class="text-sm text-blue-600 font-bold">0%</span></div></div><div class="mt-6 flex justify-end gap-3"><button type="button" id="cancelBtn" class="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300">إلغاء</button><button type="submit" id="saveBtn" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">حفظ</button></div></form></div></div>
    <div id="deleteModal" class="modal-overlay"><div class="modal-content text-center"><i class="fas fa-exclamation-triangle text-5xl text-red-500 mb-4"></i><h2 class="text-xl font-bold mb-2">هل أنت متأكد؟</h2><p class="text-gray-600 mb-6">لا يمكن التراجع عن هذا الإجراء. سيتم حذف المشروع نهائياً.</p><div class="flex justify-center gap-4"><button id="cancelDeleteBtn" class="bg-gray-200 text-gray-800 px-6 py-2 rounded-lg hover:bg-gray-300">إلغاء</button><button id="confirmDeleteBtn" class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700">نعم، احذف</button></div></div></div>
    <div id="overlay"></div>

    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getAuth, signInAnonymously, signInWithCustomToken } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { getFirestore, enableIndexedDbPersistence, collection, onSnapshot, addDoc, doc, updateDoc, deleteDoc, getDoc, serverTimestamp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

        // Main function to initialize and run the application
        async function main() {
            // These variables are expected to be injected by the environment.
            const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : null;
            const appId = typeof __app_id !== 'undefined' ? __app_id : null;
            const authToken = typeof __initial_auth_token !== 'undefined' ? __initial_auth_token : null;

            // Verify that the config is available
            if (!firebaseConfig || !firebaseConfig.apiKey || !appId) {
                document.body.innerHTML = '<div class="text-center p-8 text-red-600 text-xl">تهيئة Firebase غير صالحة. لا يمكن تشغيل التطبيق.</div>';
                console.error("Firebase config or App ID is missing from the environment.");
                return;
            }

            try {
                // Initialize Firebase
                const app = initializeApp(firebaseConfig);
                const db = getFirestore(app);
                const auth = getAuth(app);

                // Enable offline persistence
                await enableIndexedDbPersistence(db);
                
                // Sign in with the provided token or anonymously
                if (authToken) {
                    await signInWithCustomToken(auth, authToken);
                } else {
                    await signInAnonymously(auth);
                }

                console.log("Firebase initialized successfully.");
                
                // Run the application logic, passing the db instance and appId
                runApp(db, appId);

            } catch (error) {
                console.error("Firebase Initialization Failed:", error);
                document.body.innerHTML = `<div class="text-center p-8 text-red-600">فشل الاتصال بقاعدة البيانات. تأكد من صحة إعدادات Firebase. <br><small>${error.message}</small></div>`;
            }
        }

        // This function contains the entire application logic.
        function runApp(db, appId) {
            // --- DOM Elements ---
            const tableBody = document.getElementById('projectsTableBody');
            const addProjectBtn = document.getElementById('addProjectBtn');
            const projectModal = document.getElementById('projectModal');
            const deleteModal = document.getElementById('deleteModal');
            const cancelBtn = document.getElementById('cancelBtn');
            const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
            const projectForm = document.getElementById('projectForm');
            const progressSlider = document.getElementById('projectProgress');
            const progressValue = document.getElementById('progressValue');
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');

            let currentProjectIdToDelete = null;
            let progressChart, statusChart;
            let allProjects = []; // Cache for projects
            const LOCAL_STORAGE_KEY = `ikaros_projects_${appId}`; // مفتاح فريد للتخزين المحلي

            // --- Toast Notifications ---
            function showToast(message, type = 'success') {
                const toastContainer = document.getElementById('toast-container');
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                toast.textContent = message;
                toastContainer.appendChild(toast);
                setTimeout(() => { toast.classList.add('show'); }, 10);
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => toast.remove(), 500);
                }, 3000);
            }

            // --- Modal Controls ---
            function openModal() { projectModal.classList.add('active'); }
            function closeModal() { projectModal.classList.remove('active'); projectForm.reset(); document.getElementById('projectId').value = ''; progressValue.textContent = '0%'; }
            function openDeleteModal(id) { currentProjectIdToDelete = id; deleteModal.classList.add('active'); }
            function closeDeleteModal() { deleteModal.classList.remove('active'); }

            addProjectBtn.addEventListener('click', () => {
                document.getElementById('modalTitle').textContent = 'إضافة مشروع جديد';
                openModal();
            });
            cancelBtn.addEventListener('click', closeModal);
            cancelDeleteBtn.addEventListener('click', closeDeleteModal);

            // --- تحميل البيانات من التخزين المحلي عند بدء التشغيل ---
            function loadFromLocalStorage() {
                const storedProjects = localStorage.getItem(LOCAL_STORAGE_KEY);
                if (storedProjects) {
                    try {
                        allProjects = JSON.parse(storedProjects);
                        console.log("تم تحميل المشاريع من التخزين المحلي.");
                        renderProjects(allProjects);
                        updateDashboard(allProjects);
                        updateCharts(allProjects);
                        filterTable();
                    } catch (e) {
                        console.error("خطأ في قراءة البيانات من التخزين المحلي", e);
                        localStorage.removeItem(LOCAL_STORAGE_KEY); // حذف البيانات التالفة
                    }
                }
            }
            
            loadFromLocalStorage(); // استدعاء الدالة عند بدء تشغيل التطبيق

            // --- Real-time Data Listener ---
            const projectsCollectionPath = `artifacts/${appId}/public/data/projects`;
            const projectsCollection = collection(db, projectsCollectionPath);
            onSnapshot(projectsCollection, (snapshot) => {
                allProjects = [];
                snapshot.forEach(doc => allProjects.push({ id: doc.id, ...doc.data() }));
                
                // --- حفظ البيانات في التخزين المحلي بعد كل تحديث من Firebase ---
                try {
                    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(allProjects));
                } catch (e) {
                    console.error("خطأ في حفظ البيانات في التخزين المحلي", e);
                }

                renderProjects(allProjects);
                updateDashboard(allProjects);
                updateCharts(allProjects);
                filterTable();
            }, (error) => {
                console.error("Error fetching projects: ", error);
                showToast("فشل الاتصال بالخادم، يتم عرض البيانات المحلية.", "error");
                // لا تقم بمسح الجدول إذا فشل الاتصال، احتفظ بالبيانات المحلية
            });

            // --- Render Functions ---
            function renderProjects(projects) {
                tableBody.innerHTML = '';
                if (projects.length === 0) {
                    tableBody.innerHTML = `<tr><td colspan="5" class="text-center py-10 text-gray-500">لا توجد مشاريع لعرضها. ابدأ بإضافة مشروع جديد.</td></tr>`;
                    return;
                }
                const sortedProjects = projects.sort((a, b) => (b.createdAt?.seconds || 0) - (a.createdAt?.seconds || 0));

                sortedProjects.forEach(p => {
                    const statusColors = { 'جاري العمل': 'bg-green-100 text-green-800', 'متوقف': 'bg-yellow-100 text-yellow-800', 'مكتمل': 'bg-blue-100 text-blue-800' };
                    const row = `
                        <tr data-id="${p.id}">
                            <td class="px-6 py-4 whitespace-nowrap"><div class="font-medium text-gray-900">${p.name || ''}</div></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${p.client || ''}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">${Number(p.value || 0).toLocaleString('ar-KW')} د.ك</td>
                            <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusColors[p.status] || 'bg-gray-100'}">${p.status || ''}</span></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2 space-x-reverse non-printable">
                                <button class="edit-btn text-yellow-600 hover:text-yellow-900"><i class="fas fa-edit"></i></button>
                                <button class="delete-btn text-red-600 hover:text-red-900"><i class="fas fa-trash"></i></button>
                            </td>
                        </tr>`;
                    tableBody.insertAdjacentHTML('beforeend', row);
                });
            }

            function updateDashboard(projects) {
                const totalProjects = projects.length;
                const ongoing = projects.filter(p => p.status === 'جاري العمل').length;
                const completed = projects.filter(p => p.status === 'مكتمل').length;
                const totalValue = projects.reduce((sum, p) => sum + Number(p.value || 0), 0);
                document.getElementById('stats-projects').textContent = totalProjects;
                document.getElementById('stats-ongoing').textContent = ongoing;
                document.getElementById('stats-completed').textContent = completed;
                document.getElementById('stats-value').textContent = `${totalValue.toLocaleString('ar-KW')} د.ك`;
            }
            
            function updateCharts(projects) {
                const progressCtx = document.getElementById('progressChart').getContext('2d');
                const statusCtx = document.getElementById('statusChart').getContext('2d');
                
                if (progressChart) progressChart.destroy();
                progressChart = new Chart(progressCtx, {
                    type: 'bar',
                    data: { labels: projects.map(p => p.name), datasets: [{ label: 'نسبة الإنجاز', data: projects.map(p => p.progress), backgroundColor: 'rgba(59, 130, 246, 0.7)' }] },
                    options: { responsive: true, maintainAspectRatio: false, indexAxis: 'y', scales: { x: { beginAtZero: true, max: 100 } } }
                });

                const statusCounts = projects.reduce((acc, p) => { acc[p.status] = (acc[p.status] || 0) + 1; return acc; }, {});
                if (statusChart) statusChart.destroy();
                statusChart = new Chart(statusCtx, {
                    type: 'doughnut',
                    data: { labels: Object.keys(statusCounts), datasets: [{ data: Object.values(statusCounts), backgroundColor: ['#16a34a', '#f59e0b', '#3b82f6', '#6b7280'] }] },
                    options: { responsive: true, maintainAspectRatio: false }
                });
            }

            // --- CRUD Operations ---
            projectForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const id = document.getElementById('projectId').value;
                const projectData = {
                    name: document.getElementById('projectName').value,
                    client: document.getElementById('clientName').value,
                    value: parseFloat(document.getElementById('projectValue').value) || 0,
                    status: document.getElementById('projectStatus').value,
                    progress: parseInt(document.getElementById('projectProgress').value) || 0
                };
                try {
                    if (id) {
                        await updateDoc(doc(db, projectsCollectionPath, id), projectData);
                        showToast("تم تحديث المشروع بنجاح!");
                    } else {
                        projectData.createdAt = serverTimestamp();
                        await addDoc(collection(db, projectsCollectionPath), projectData);
                        showToast("تمت إضافة المشروع بنجاح!");
                    }
                    closeModal();
                } catch (error) {
                    console.error("Error saving project: ", error);
                    showToast("حدث خطأ أثناء الحفظ", "error");
                }
            });

            tableBody.addEventListener('click', async (e) => {
                const target = e.target.closest('button');
                if (!target) return;
                const row = target.closest('tr');
                const id = row.dataset.id;
                if (target.classList.contains('edit-btn')) {
                    const docRef = doc(db, projectsCollectionPath, id);
                    const docSnap = await getDoc(docRef);
                    if (docSnap.exists()) {
                        const data = docSnap.data();
                        document.getElementById('modalTitle').textContent = 'تعديل مشروع';
                        document.getElementById('projectId').value = id;
                        document.getElementById('projectName').value = data.name;
                        document.getElementById('clientName').value = data.client;
                        document.getElementById('projectValue').value = data.value;
                        document.getElementById('projectStatus').value = data.status;
                        progressSlider.value = data.progress;
                        progressValue.textContent = `${data.progress}%`;
                        openModal();
                    }
                } else if (target.classList.contains('delete-btn')) {
                    openDeleteModal(id);
                }
            });

            document.getElementById('confirmDeleteBtn').addEventListener('click', async () => {
                if (currentProjectIdToDelete) {
                    try {
                        await deleteDoc(doc(db, projectsCollectionPath, currentProjectIdToDelete));
                        showToast("تم حذف المشروع بنجاح!");
                        closeDeleteModal();
                    } catch (error) {
                        console.error("Error deleting project: ", error);
                        showToast("حدث خطأ أثناء الحذف", "error");
                    }
                }
            });

            // --- UI Helpers & Filtering ---
            progressSlider.addEventListener('input', (e) => { progressValue.textContent = `${e.target.value}%`; });
            function filterTable() {
                const searchValue = searchInput.value.toLowerCase().trim();
                const statusValue = statusFilter.value;
                const rows = tableBody.getElementsByTagName('tr');
                let visibleRows = 0;
                for (let row of rows) {
                    if (row.getElementsByTagName('td').length === 1) continue;
                    const statusText = row.querySelector('td:nth-child(4) span').textContent.trim();
                    const rowText = row.textContent.toLowerCase();
                    const searchMatch = rowText.includes(searchValue);
                    const statusMatch = statusValue === "" || statusText === statusValue;
                    if (searchMatch && statusMatch) {
                        row.style.display = "";
                        visibleRows++;
                    } else {
                        row.style.display = "none";
                    }
                }
                const noProjectsRow = tableBody.querySelector('td[colspan="5"]');
                if (noProjectsRow) {
                    if (allProjects.length === 0) noProjectsRow.parentElement.style.display = '';
                    else noProjectsRow.parentElement.style.display = visibleRows === 0 ? '' : 'none';
                }
            }
            searchInput.addEventListener('keyup', filterTable);
            statusFilter.addEventListener('change', filterTable);
            document.getElementById('printBtn').addEventListener('click', () => window.print());

            // --- Sidebar Logic ---
            const sidebar = document.getElementById('sidebar');
            document.getElementById('toggleSidebar').addEventListener('click', () => sidebar.classList.toggle('collapsed'));
            document.getElementById('mobileMenuButton').addEventListener('click', (e) => { e.stopPropagation(); sidebar.classList.add('open'); document.getElementById('overlay').classList.add('active'); });
            document.getElementById('overlay').addEventListener('click', () => { sidebar.classList.remove('open'); document.getElementById('overlay').classList.remove('active'); });
        }
        
        // Run the main function when the DOM is ready
        document.addEventListener('DOMContentLoaded', main);
    </script>
</body>
</html>
