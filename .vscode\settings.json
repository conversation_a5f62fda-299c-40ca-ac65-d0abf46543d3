{
    // Browser settings
    "liveServer.settings.CustomBrowser": "chrome",
    "liveServer.settings.ChromeDebuggingAttachment": false,
    
    // File associations
    "files.associations": {
        "*.html": "html",
        "*.css": "css",
        "*.js": "javascript"
    },
    
    // Default browser for opening files
    "workbench.editorAssociations": {
        "*.html": "default"
    },
    
    // Terminal settings
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.profiles.windows": {
        "PowerShell": {
            "source": "PowerShell",
            "icon": "terminal-powershell"
        },
        "Command Prompt": {
            "path": [
                "${env:windir}\\Sysnative\\cmd.exe",
                "${env:windir}\\System32\\cmd.exe"
            ],
            "args": [],
            "icon": "terminal-cmd"
        }
    },
    
    // External browser paths (Windows)
    "open-in-browser.default": "chrome",
    "browser-preview.startUrl": "http://localhost:3000",
    
    // Live Server settings
    "liveServer.settings.donotShowInfoMsg": true,
    "liveServer.settings.donotVerifyTags": true,
    "liveServer.settings.port": 5500,
    "liveServer.settings.root": "/",
    "liveServer.settings.file": "index.html",
    
    // Security settings
    "security.workspace.trust.untrustedFiles": "open",
    "security.workspace.trust.banner": "never",
    "security.workspace.trust.startupPrompt": "never",
    
    // Auto save
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    
    // Formatting
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    
    // HTML settings
    "html.format.indentInnerHtml": true,
    "html.format.wrapLineLength": 120,
    
    // Emmet
    "emmet.includeLanguages": {
        "javascript": "javascriptreact",
        "vue-html": "html",
        "razor": "html",
        "plaintext": "jade"
    }
}
